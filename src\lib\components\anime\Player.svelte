<script>
	import { goto } from '$app/navigation';
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { X, ChevronLeft, ChevronRight } from 'lucide-svelte';
	import { Button } from '$lib/components/ui/button';
	import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '$lib/components/ui/dialog';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { formatDistanceToNow, addHours, setHours } from 'date-fns';
	import { pl } from 'date-fns/locale';
	import InfiniteComments from '$lib/components/containers/comments/InfiniteComments.svelte';
	import { writable } from 'svelte/store';
	import { page } from '$app/stores';
	import VideoPlayer from '$lib/components/anime/VideoPlayer.svelte';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	import GroupSelector from '$lib/components/anime/GroupSelector.svelte';
	import { userStore } from '$lib/stores/userLogin';
	import { progressStore } from '$lib/stores/progressStore';
	import { toast } from 'svelte-sonner';
	import { cacheKeys, getCachedData, setCachedData } from '$lib/utils/cacheUtils';
	import { fade, scale, slide } from 'svelte/transition';
	import tinycolor from 'tinycolor2';

	export let anime;
	export let episode;
	export let allEpisodes;
	export let nextEpisodeData;
	export let dominantColor;
	const dispatch = createEventDispatcher();

	let initialized = false;
	let isSpoiler = false;
	let newComment = '';
	let isSubmittingComment = false;
	let showUserModal = false;
	let isMobile = false;
	let selectedGroup = null;
	let selectedGroupUrl = null;
	let selectedPlayerType = null;
	let currentPlayerType = null; // Track the current player type for display
	let translationGroups = []; // Store translation groups data for links
	const commentsStore = writable([]);

	$: isLoggedIn = $userStore?.role === 'authenticated';
	$: hasSecondaryLink = episode && episode.secondarySource && Object.keys(episode.secondarySource).length > 0;
	$: hasExternalPlayerLink = episode && episode.external_player_link;
	$: hasTranslatingGroup = episode && episode.translating_group && episode.translating_group !== 'lycoris_cafe';
	$: hasPlayerSource = episode && episode.player_source && episode.player_source !== 'lycoris_cafe';
	$: hasOtherGroups = episode && episode.other_groups && Object.keys(episode.other_groups).length > 0;
	$: hasAllGroups = episode && episode.allGroups && Object.keys(episode.allGroups).length > 0;
	$: showGroupSelector = hasAllGroups || hasSecondaryLink || hasExternalPlayerLink || hasTranslatingGroup || hasOtherGroups;
	$: isGroupSelectorCollapsed = false; // Always start expanded to show all options

	function isWkrotce(ep) {
		return new Date(ep.airDate) <= new Date() && !allEpisodes.some((e) => e.number === ep.number);
	}

	function getEpisodeTimeStatus(airDate, isDelayed = false, episodeNumber = null) {
		const episodeDate = new Date(airDate);

		if (episodeDate.toString() === 'Invalid Date') {
			return '';
		}

		const now = new Date();
		const isEpisodeReleased = episodeNumber ? allEpisodes.some((ep) => ep.number === episodeNumber) : false;
		const isComingSoon = episodeDate <= now && !isEpisodeReleased;
		const isUpcoming = episodeDate > now;

		// Check if this anime has delayed airing and episode is either coming soon or upcoming
		if ((isDelayed || (anime && anime.delayedAiring)) && (isComingSoon || isUpcoming)) {
			return 'delayed-airing-text';
		}

		if (episodeDate <= now) {
			return formatDistanceToNow(episodeDate - 3600000, { addSuffix: true, locale: pl });
		} else {
			return `Wychodzi ${formatDistanceToNow(episodeDate - 3600000, { addSuffix: true, locale: pl })}`;
		}
	}

	async function initializeComments() {
		try {
			const response = await fetch(`/api/comments?animeId=${anime.id}&episodeNumber=${episode.number}`);
			if (!response.ok) throw new Error('Failed to fetch comments');
			const { comments: initialComments } = await response.json();
			commentsStore.set(Array.isArray(initialComments) ? initialComments : []);
			initialized = true;
		} catch (error) {
			console.error('Error loading initial comments:', error);
			toast.error('Nie udało się załadować komentarzy');
			commentsStore.set([]);
		}
	}

	async function refreshComments() {
		try {
			const response = await fetch(`/api/comments?animeId=${anime.id}&episodeNumber=${episode.number}`);
			if (!response.ok) throw new Error('Failed to fetch comments');
			const { comments: freshComments } = await response.json();
			commentsStore.set(Array.isArray(freshComments) ? freshComments : []);
		} catch (error) {
			console.error('Error refreshing comments:', error);
			toast.error('Nie udało się odświeżyć komentarzy');
		}
	}

	async function handleSubmit(e) {
		if (e) e.preventDefault();

		if (!isLoggedIn) {
			showUserModal = true;
			return;
		}

		if (!newComment.trim()) return;

		try {
			toast.info('Dodawanie komentarza...');
			isSubmittingComment = true;
			const response = await fetch('/api/comments/submit', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					content: newComment.trim(),
					anilistId: anime.id,
					episodeNumber: episode.number,
					isSpoiler: isSpoiler
				})
			});

			const data = await response.json();

			if (!response.ok) {
				// Handle ban message specifically
				if (response.status === 403 && data.message?.includes('zbanowane')) {
					const banReason = data.details || 'brak';
					throw new Error(`${data.message} ${banReason}`.trim());
				}
				throw new Error(data.message || 'Error posting comment');
			}

			// Refresh all comments after submitting a new one
			await refreshComments();

			newComment = '';
			isSpoiler = false;

			if (data.is_pending_review) {
				toast.warning('Komentarz został dodany i oczekuje na moderację');
			} else {
				toast.success('Komentarz został dodany');
			}
		} catch (error) {
			console.error('Error submitting comment:', error);
			toast.error(error.message || 'Nie udało się dodać komentarza');
		} finally {
			isSubmittingComment = false;
		}
	}

	// Function to handle episode changes
	async function changeEpisode(episodeNumber) {
		const baseUrl = getBaseUrl();
		await goto(`${baseUrl}${episodeNumber}`, {
			noScroll: true,
			invalidateAll: true
		});
		dispatch('changeEpisode', episodeNumber);
	}

	function nextEpisode() {
		const currentIndex = allEpisodes.findIndex((ep) => ep.number === episode.number);
		if (currentIndex < allEpisodes.length - 1) {
			const nextEp = allEpisodes[currentIndex + 1];
			changeEpisode(nextEp.number);
		}
	}

	function previousEpisode() {
		const currentIndex = allEpisodes.findIndex((ep) => ep.number === episode.number);
		if (currentIndex > 0) {
			const prevEp = allEpisodes[currentIndex - 1];
			changeEpisode(prevEp.number);
		}
	}

	async function handleEpisodeSelect(episodeNumber) {
		await changeEpisode(episodeNumber);
	}

	function autoResize(e) {
		const textarea = e.target;
		textarea.style.height = 'auto';
		textarea.style.height = textarea.scrollHeight + 'px';
	}

	function handleKeydown(e) {
		if (e.key === 'Enter') {
			e.stopPropagation();
		}
	}

	function closePlayer() {
		dispatch('close');
	}

	function getBaseUrl() {
		const currentUrl = $page.url.pathname;
		return currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
	}

	async function handleGroupSelect(event) {
		const { group, playerInfo } = event.detail;
		selectedGroup = group;

		console.log('=== GROUP SELECTION DEBUG ===');
		console.log('Selected group:', group);
		console.log('Player info:', playerInfo);
		console.log('Has secondary link:', hasSecondaryLink);
		console.log('Episode secondary source:', episode.secondarySource);

		// Fetch the correct episode data for the selected translating group and player
		try {
			console.log('Fetching episode data for group:', group, 'with player info:', playerInfo);

			// Build API URL with parameters
			let apiUrl = `/api/anime/${episode.anilist_id}/episode/${episode.number}?translatingGroup=${encodeURIComponent(group)}`;

			// Add player source if available and not lycoris.cafe
			if (playerInfo && playerInfo.type && group !== 'lycoris.cafe') {
				apiUrl += `&playerSource=${encodeURIComponent(playerInfo.type)}`;
			}

			console.log('API URL:', apiUrl);
			const response = await fetch(apiUrl);

			if (response.ok) {
				const data = await response.json();
				console.log('Fetched episode data:', data.episode);

				// Update the episode data with the correct translating group data
				episode = { ...episode, ...data.episode };
				console.log('Updated episode data:', episode);
			} else {
				console.error('Failed to fetch episode data for group:', group);
			}
		} catch (error) {
			console.error('Error fetching episode data:', error);
		}

		if (group === 'lycoris.cafe' && episode.secondarySource && Object.keys(episode.secondarySource).length > 0) {
			// Use secondary link for lycoris.cafe - get the best available quality
			selectedGroupUrl = episode.secondarySource?.FHD || episode.secondarySource?.HD || episode.secondarySource?.SD || null;
			selectedPlayerType = 'lycoris';
			currentPlayerType = 'lycoris.cafe';
			console.log('Using lycoris.cafe player with URL:', selectedGroupUrl);
		} else if (playerInfo) {
			selectedGroupUrl = playerInfo.url;
			selectedPlayerType = playerInfo.type || 'external';
			currentPlayerType = playerInfo.type || 'external';
			console.log('Using external player:', selectedPlayerType, 'with URL:', selectedGroupUrl);
		} else {
			selectedGroupUrl = null;
			selectedPlayerType = null;
			currentPlayerType = null;
			console.log('No player selected');
		}
		console.log('Final state - selectedGroup:', selectedGroup, 'selectedPlayerType:', selectedPlayerType, 'selectedGroupUrl:', selectedGroupUrl);
		console.log('=== END GROUP SELECTION DEBUG ===');
	}

	function handleGroupsUpdated(event) {
		const { allGroups } = event.detail;
		// Update the episode's allGroups data
		episode = {
			...episode,
			allGroups: allGroups
		};
	}

	// Function to get sorted group names with lycoris.cafe first, then by date of first player (oldest first)
	function getSortedGroupNames(allGroups) {
		if (!allGroups) return [];

		const groupNames = Object.keys(allGroups);

		// Sort to ensure lycoris.cafe is always first, then by date of adding first player (oldest first)
		return groupNames.sort((a, b) => {
			if (a === 'lycoris.cafe') return -1;
			if (b === 'lycoris.cafe') return 1;

			// For other groups, sort by date_added of first player (oldest first)
			const groupAData = allGroups[a];
			const groupBData = allGroups[b];

			// Get the earliest date_added from all players in each group (first player added)
			const getFirstPlayerDate = (groupData) => {
				if (!groupData?.players || groupData.players.length === 0) return '9999-12-31'; // Far future for groups with no players

				// Find the earliest date among all players (first player added to this group)
				const dates = groupData.players
					.map((player) => player.date_added)
					.filter((date) => date) // Remove null/undefined dates
					.sort(); // Sort dates ascending (earliest first)

				return dates[0] || '9999-12-31'; // Return earliest date (first player) or far future fallback
			};

			const firstPlayerDateA = getFirstPlayerDate(groupAData);
			const firstPlayerDateB = getFirstPlayerDate(groupBData);

			// Sort by date (oldest first - groups that added their first player earliest appear first)
			// So okami-subs (2025-05-18) appears before wbijam.pl (2025-06-09)
			return new Date(firstPlayerDateA) - new Date(firstPlayerDateB);
		});
	}

	// Function to get group link data
	function getGroupLink(groupName) {
		if (groupName === 'lycoris.cafe') {
			return {
				label: 'Discord',
				link: 'https://lycoris.cafe/discord'
			};
		}

		// Find the group in translationGroups data
		const group = translationGroups.find((g) => g.name === groupName);
		if (group && group.link) {
			try {
				// Parse the JSONB link field
				const linkData = typeof group.link === 'string' ? JSON.parse(group.link) : group.link;
				if (linkData && linkData.label && linkData.link) {
					return linkData;
				}
			} catch (e) {
				console.warn('Failed to parse group link data:', e);
			}
		}

		return null; // No link available
	}

	// Fetch translation groups data for links
	async function fetchTranslationGroups() {
		try {
			const response = await fetch('/api/translation-groups');
			if (response.ok) {
				translationGroups = await response.json();
			}
		} catch (error) {
			console.error('Failed to fetch translation groups:', error);
		}
	}

	const ANILIST_API = 'https://graphql.anilist.co';
	const MEDIA_ENTRY_QUERY = `
query ($mediaId: Int, $userId: Int) {
  Media(id: $mediaId) {
    id
    title {
      romaji 
      english
    }
  }
  MediaList(userId: $userId, mediaId: $mediaId) {
    id
    status
    score
    progress
    startedAt {
      year
      month
      day
    }
    completedAt {
      year
      month
      day
    }
    repeat
    notes
    updatedAt
  }
  Viewer {
    id
    mediaListOptions {
      scoreFormat
    }
  }
}`;

	async function refreshAnilistToken() {
		try {
			const response = await fetch('/api/anilist/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			// Update user store with new token
			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					anilist_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing token:', error);
			toast.error('Nie udało się odświeżyć tokena');
			return null;
		}
	}

	// Add this function to fetch AniList media data
	async function fetchAniListMediaData() {
		if (!isLoggedIn) return null;

		try {
			const cacheKey = `${cacheKeys.ANILIST_MEDIA}${anime.id}`;

			// Check cache first
			const cachedData = getCachedData(cacheKey);
			if (cachedData) {
				return cachedData;
			}

			let token = $userStore.user_metadata?.anilist_token;
			const userId = $userStore.user_metadata?.id;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshAnilistToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token || !userId) {
				return null;
			}

			const response = await fetch(ANILIST_API, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json',
					Authorization: `Bearer ${token}`
				},
				body: JSON.stringify({
					query: MEDIA_ENTRY_QUERY,
					variables: {
						mediaId: parseInt(anime.id),
						userId: parseInt(userId)
					}
				})
			});

			const data = await response.json();

			if (data.errors && data.data?.MediaList === null) {
				// Normal case - anime not in user's list yet
				return {
					status: 'planning',
					score: 0,
					episodeProgress: 0,
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
			}

			const mediaList = data.data.MediaList;
			if (!mediaList) {
				return {
					status: 'planning',
					score: 0,
					episodeProgress: 0,
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
			}

			const formattedData = {
				status: mediaList.status?.toLowerCase() || 'planning',
				score: mediaList.score || 0,
				episodeProgress: mediaList.progress || 0,
				startDate: mediaList.startedAt?.year ? new Date(mediaList.startedAt.year, mediaList.startedAt.month - 1, mediaList.startedAt.day) : null,
				finishDate: mediaList.completedAt?.year ? new Date(mediaList.completedAt.year, mediaList.completedAt.month - 1, mediaList.completedAt.day) : null,
				totalRewatches: mediaList.repeat || '',
				notes: mediaList.notes || ''
			};

			setCachedData(cacheKey, formattedData);
			return formattedData;
		} catch (error) {
			console.error('Error fetching AniList data:', error);
			return null;
		}
	}

	async function fetchMALMediaData() {
		if (!isLoggedIn) return null;

		try {
			const cacheKey = `${cacheKeys.MAL_MEDIA}${anime.id}`;

			// Check cache first
			const cachedData = getCachedData(cacheKey);
			if (cachedData) {
				return cachedData;
			}

			let token = $userStore.user_metadata?.mal_token;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshMALToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token) {
				return null;
			}

			// Use server-side proxy instead of direct MAL API request
			const response = await fetch('/api/mal/proxy/anime-details', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					animeId: anime.mal_id || anime.id,
					token
				})
			});

			if (!response.ok) {
				throw new Error('Failed to fetch MAL data');
			}

			const data = await response.json();
			const myStatus = data.my_list_status;

			if (!myStatus) {
				// New anime not in list yet
				return {
					status: 'planning',
					score: 0,
					episodeProgress: 0,
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
			}

			const formattedData = {
				status: mapMALStatusToAniList(myStatus.status),
				score: myStatus.score || 0,
				episodeProgress: myStatus.num_episodes_watched || 0,
				startDate: myStatus.start_date ? new Date(myStatus.start_date) : null,
				finishDate: myStatus.finish_date ? new Date(myStatus.finish_date) : null,
				totalRewatches: myStatus.num_times_rewatched || '',
				notes: myStatus.comments || ''
			};

			setCachedData(cacheKey, formattedData);
			return formattedData;
		} catch (error) {
			console.error('Error fetching MAL data:', error);
			return null;
		}
	}

	function mapMALStatusToAniList(status) {
		const statusMap = {
			watching: 'current',
			completed: 'completed',
			on_hold: 'on-hold',
			dropped: 'dropped',
			plan_to_watch: 'planning'
		};
		return statusMap[status] || 'planning';
	}

	async function refreshMALToken() {
		try {
			const response = await fetch('/api/mal/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					mal_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing MAL token:', error);
			toast.error('Nie udało się odświeżyć tokena MAL. Możesz spróbować wylogować i zalogować się ponownie aby naprawić ten błąd.', {
				duration: Number.POSITIVE_INFINITY
			});
			return null;
		}
	}

	function getWatchedClass(episodeNumber) {
		const anilistProgress = anime.episodeProgress || 0;
		const localProgress = $progressStore[anime.id]?.[episodeNumber]?.progress || 0;

		if (episodeNumber <= anilistProgress) {
			return 'opacity-60';
		}

		if (episodeNumber === anilistProgress + 1) {
			return localProgress > 85 ? 'opacity-60' : 'opacity-100';
		}

		return 'opacity-100';
	}

	function getPercentageCompletion(episodeNumber) {
		const anilistProgress = anime.status === 'completed' ? anime.totalEpisodes : anime.episodeProgress || 0;
		const localProgress = $progressStore[anime.id]?.[episodeNumber]?.progress || 0;

		// Episode is completed in AniList
		if (episodeNumber <= anilistProgress) {
			return 100;
		}

		if (localProgress > 85) {
			return 100; // Mark as complete if >85%
		}
		return localProgress;
	}

	async function enrichAnimeWithAniListData() {
		if (!isLoggedIn) return;

		try {
			const provider = $userStore?.user_metadata?.provider;
			let data;

			if (provider === 'anilist') {
				data = await fetchAniListMediaData();
			} else if (provider === 'mal') {
				data = await fetchMALMediaData();
			}

			if (data) {
				anime = {
					...anime,
					...data
				};
			}
		} catch (error) {
			console.error('Error enriching anime data:', error);
		}
	}

	onMount(() => {
		initializeComments();
		fetchTranslationGroups(); // Fetch translation groups for links

		// Detect mobile devices
		isMobile = window.innerWidth < 768;

		// Add resize listener to update isMobile
		const handleResize = () => {
			isMobile = window.innerWidth < 768;
		};

		window.addEventListener('resize', handleResize);

		// No auto-selection - always show group selector with all available options

		// Fetch AniList data if user is logged in
		if (isLoggedIn) {
			enrichAnimeWithAniListData();
		}

		// Cleanup function
		return () => {
			window.removeEventListener('resize', handleResize);
		};
	});

	onDestroy(() => {
		// Additional cleanup if needed
	});

	function handleEpisodeError(error) {
		dispatch('error', error);
	}

	async function handleEpisodeEnd() {
		dispatch('episodeEnd');

		// Invalidate the cache for this anime
		const cacheKey = `${cacheKeys.ANILIST_MEDIA}${anime.id}`;
		localStorage.removeItem(cacheKey);

		// Re-fetch and cache new data
		await enrichAnimeWithAniListData();
	}
</script>

<div class="fixed inset-0 z-50 flex flex-col" role="dialog" aria-labelledby="episode-title">
	<div class="fixed top-[7px] right-[16px] z-100">
		<Button class="p-2 text-white bg-transparent rounded-full cursor-pointer hover:bg-gray-700" on:click={closePlayer} aria-label="Zamknij odtwarzacz">
			<X class="w-6 h-6" />
		</Button>
	</div>

	<div class="flex-1 overflow-y-auto bg-black backdrop-blur-xs">
		<div class="w-full min-h-screen bg-gray-900 shadow-lg">
			<div class="flex flex-col lg:flex-row">
				<div class="w-full lg:w-3/4">
					<!-- Mobile Group Selector - shown before episodes list when no group is selected -->
					{#if showGroupSelector && !selectedGroup}
						<div class="w-full mb-4 bg-gray-900">
							<GroupSelector
								otherGroups={hasAllGroups ? episode.allGroups : episode.other_groups}
								episodeId={episode.id}
								{selectedGroup}
								{episode}
								on:select={handleGroupSelect}
								on:groupsUpdated={handleGroupsUpdated}
							/>
						</div>
					{/if}

					{#if showGroupSelector && !selectedGroupUrl && selectedGroup}
						<div class="w-full mb-4 bg-gray-900">
							<GroupSelector
								otherGroups={hasAllGroups ? episode.allGroups : episode.other_groups}
								episodeId={episode.id}
								{selectedGroup}
								{episode}
								on:select={handleGroupSelect}
								on:groupsUpdated={handleGroupsUpdated}
							/>
						</div>
					{/if}

					<!-- Player container - hidden on mobile when no group is selected -->
					<div class="relative w-full bg-black {showGroupSelector && !selectedGroup ? 'hidden lg:block' : ''}" style="aspect-ratio: 16/9;">
						{#if selectedGroupUrl}
							<div class="absolute inset-0">
								{#key episode.number + (selectedGroupUrl || '')}
									<VideoPlayer
										episodeInfo={episode}
										{nextEpisodeData}
										{selectedPlayerType}
										selectedPlayerUrl={selectedGroupUrl}
										{selectedGroup}
										on:error={handleEpisodeError}
										on:ended={handleEpisodeEnd}
									/>
								{/key}
							</div>
						{:else if showGroupSelector && !selectedGroup}
							<!-- Desktop version - show group selector in player area -->
							<div class="absolute inset-0 flex flex-col items-start justify-start overflow-y-auto bg-gray-900 player-container-scroll">
								<div class="flex flex-col w-full max-w-4xl min-h-full px-4 pt-4 mx-auto">
									<GroupSelector
										otherGroups={hasAllGroups ? episode.allGroups : episode.other_groups || {}}
										episodeId={episode.id}
										{selectedGroup}
										{episode}
										on:select={handleGroupSelect}
										on:groupsUpdated={handleGroupsUpdated}
									/>
								</div>
							</div>
						{:else}
							<div class="absolute inset-0 flex items-center justify-center bg-gray-900">
								<p class="text-lg text-gray-400">Wybierz grupę tłumaczeniową, aby obejrzeć odcinek</p>
							</div>
						{/if}

						{#if !showGroupSelector || selectedGroup}
							<button
								class="absolute p-2 text-white transition-opacity -translate-y-1/2 bg-gray-800 rounded-full opacity-50 top-1/2 left-4 hover:cursor-pointer hover:bg-gray-700 hover:opacity-100 disabled:cursor-not-allowed disabled:opacity-30"
								aria-label="Poprzedni odcinek"
								on:click={previousEpisode}
								disabled={episode.number === 1}
							>
								<ChevronLeft class="w-6 h-6" aria-hidden="true" />
								<span class="sr-only">Poprzedni</span>
							</button>
							<button
								class="absolute p-2 text-white transition-opacity -translate-y-1/2 bg-gray-800 rounded-full opacity-50 top-1/2 right-4 hover:cursor-pointer hover:bg-gray-700 hover:opacity-100 disabled:cursor-not-allowed disabled:opacity-30"
								aria-label="Następny odcinek"
								on:click={nextEpisode}
								disabled={episode.number === allEpisodes.length}
							>
								<ChevronRight class="w-6 h-6" aria-hidden="true" />
								<span class="sr-only">Następny</span>
							</button>
						{/if}
					</div>

					<div class="p-4 pb-6 bg-gray-800 lg:hidden">
						<h3 class="mb-4 text-lg font-semibold text-white">Odcinki</h3>
						<ul class="space-y-2">
							{#each Array(anime.totalEpisodes || 12) as _, i}
								{@const episodeNum = i + 1}
								{@const ep = allEpisodes.find((e) => e.number === episodeNum) || {
									number: episodeNum,
									title: `Odcinek ${episodeNum}`,
									thumbnail: anime.poster,
									airDate:
										anime.nextAiringEpisode?.episode === episodeNum ? new Date(anime.nextAiringEpisode.airingAt) : new Date(Date.now() + (episodeNum - allEpisodes.length) * 7 * 24 * 60 * 60 * 1000),
									progress: 0,
									watched: false
								}}
								{@const now = new Date()}
								{@const episodeDate = new Date(ep.airDate)}
								{@const isReleased = episodeDate <= now}
								{@const wkrotce = isWkrotce(ep)}
								{#if isReleased}
									<li>
										<a
											href={`${getBaseUrl()}${ep.number}`}
											on:click|preventDefault={() => (wkrotce ? null : handleEpisodeSelect(ep.number))}
											class="group flex items-center space-x-2 rounded-md p-2 transition-all hover:bg-gray-700
											{ep.number === episode.number ? 'bg-gray-700' : ''}
											{wkrotce ? 'cursor-not-allowed' : ''}"
											style="opacity: {wkrotce ? '0.4' : ep.progress === 100 || ep.watched ? '0.5' : '0.9'}"
											aria-current={ep.number === episode.number ? 'true' : 'false'}
											aria-label="Odcinek {ep.number}: {ep.title}"
										>
											<div class="w-24 h-16 overflow-hidden bg-gray-800 rounded shrink-0">
												<img src={ep.thumbnail} alt="" class="object-cover w-full h-full" />
											</div>
											<div class="flex flex-col flex-1 min-w-0">
												<h4 class="truncate text-sm font-semibold text-white {getWatchedClass(ep.number)}">
													{ep.number}. {ep.title}
												</h4>
												<span class="text-xs text-gray-400">
													{#if getEpisodeTimeStatus(ep.airDate, false, ep.number) === 'delayed-airing-text'}
														<span class="sm:hidden">Poza harmonogramem</span>
														<span class="hidden sm:inline">Poza harmonogramem - jak zrobimy to będzie</span>
													{:else}
														{getEpisodeTimeStatus(ep.airDate, false, ep.number)}
													{/if}
												</span>
												<div class="w-full h-1 mt-1 overflow-hidden bg-gray-600 rounded-full">
													<div
														class="h-full rounded-full"
														style="width: {getPercentageCompletion(ep.number)}%; 
														background-color: {dominantColor};"
														role="progressbar"
														aria-valuenow={getPercentageCompletion(ep.number)}
														aria-valuemin="0"
														aria-valuemax="100"
														aria-label="Postęp odcinka {ep.number}: {getPercentageCompletion(ep.number)}%"
													/>
												</div>
											</div>
										</a>
									</li>
								{/if}
							{/each}
						</ul>
					</div>

					<div class="relative p-4 bg-gray-900">
						<div class="sticky top-0 z-10 pb-4 bg-gray-900">
							<div class="flex items-center justify-between mb-4">
								<h3 class="text-lg font-semibold text-white">Komentarze</h3>

								<!-- Compact Group/Player Selector -->
								{#if showGroupSelector && selectedGroup}
									<div class="flex items-center gap-2">
										<DropdownMenu.Root>
											<DropdownMenu.Trigger asChild let:builder>
												<Button builders={[builder]} variant="outline" size="sm" class="text-xs text-gray-300 transition-all duration-200 border-gray-600 group hover:bg-gray-700">
													<img src="https://lycoris.cafe/logo.png" alt="{selectedGroup} logo" class="w-4 h-4 mr-2 rounded" />
													<span class="hidden sm:inline">{selectedGroup}</span>
													<span class="sm:hidden">{selectedGroup.slice(0, 8)}{selectedGroup.length > 8 ? '...' : ''}</span>
													{#if hasAllGroups && episode.allGroups[selectedGroup]?.quality}
														<span class="hidden ml-1 text-gray-400 md:inline">({episode.allGroups[selectedGroup].quality})</span>
													{/if}
													<svg class="ml-2 h-3 w-3 transition-transform duration-200 group-data-[state=open]:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
														<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
													</svg>
												</Button>
											</DropdownMenu.Trigger>
											<DropdownMenu.Content class="min-w-[200px] border-gray-600 bg-gray-800">
												<!-- Current Selection Header -->
												<div class="px-3 py-2 border-b border-gray-600">
													<div class="flex items-center space-x-2">
														<img src="https://lycoris.cafe/logo.png" alt="{selectedGroup} logo" class="w-5 h-5 rounded" />
														<div class="flex flex-col">
															<span class="text-sm font-medium text-white">{selectedGroup}</span>
															{#if hasAllGroups && episode.allGroups[selectedGroup]?.quality}
																<span class="text-xs text-gray-400">{episode.allGroups[selectedGroup].quality}</span>
															{/if}
														</div>
													</div>
												</div>

												<!-- Player Type Selection (if multiple players) -->
												{#if hasAllGroups && episode.allGroups[selectedGroup]?.players?.length > 1}
													<div class="px-3 py-2 border-b border-gray-600">
														<p class="mb-2 text-xs text-gray-400">Odtwarzacz:</p>
														{#each episode.allGroups[selectedGroup].players as player}
															{@const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language')}
															{@const playerUrl = player[playerType]}
															<DropdownMenu.Item
																class="cursor-pointer py-1 text-sm {currentPlayerType === playerType || selectedPlayerType === playerType ? 'no-hover-bg bg-gray-700 text-white' : 'text-gray-300'}"
																on:click={() => {
																	handleGroupSelect({
																		detail: {
																			group: selectedGroup,
																			playerInfo: {
																				type: playerType,
																				url: playerUrl
																			}
																		}
																	});
																}}
															>
																<span class="transition-colors {currentPlayerType === playerType || selectedPlayerType === playerType ? '' : 'hover:text-blue-400'}">{playerType}</span>
																{#if currentPlayerType === playerType || selectedPlayerType === playerType}
																	<span class="ml-auto text-[#ee8585]">✓</span>
																{/if}
															</DropdownMenu.Item>
														{/each}
													</div>
												{/if}

												<!-- Available Groups -->
												{#if hasAllGroups && Object.keys(episode.allGroups).length > 1}
													<div class="px-3 py-2">
														<p class="mb-2 text-xs text-gray-400">Dostępne grupy:</p>
														{#each getSortedGroupNames(episode.allGroups) as groupName}
															<DropdownMenu.Item
																class="cursor-pointer py-1 text-sm {selectedGroup === groupName ? 'no-hover-bg bg-gray-700 text-white' : 'text-gray-300'}"
																on:click={() => {
																	const groupData = episode.allGroups[groupName];
																	const bestPlayer = groupData.players[0];
																	const playerType = Object.keys(bestPlayer).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language');
																	const playerUrl = bestPlayer[playerType];

																	handleGroupSelect({
																		detail: {
																			group: groupName,
																			playerInfo: {
																				type: playerType,
																				url: playerUrl
																			}
																		}
																	});
																}}
															>
																<div class="flex items-center space-x-2">
																	<img src="https://lycoris.cafe/logo.png" alt="{groupName} logo" class="w-4 h-4 rounded" />
																	<span class="transition-colors {selectedGroup === groupName ? '' : 'hover:text-blue-400'}">{groupName}</span>
																	{#if episode.allGroups[groupName]?.quality}
																		<span class="text-xs text-gray-400">({episode.allGroups[groupName].quality})</span>
																	{/if}
																	{#if selectedGroup === groupName}
																		<span class="ml-auto text-[#ee8585]">✓</span>
																	{/if}
																</div>
															</DropdownMenu.Item>
														{/each}
													</div>
												{/if}
											</DropdownMenu.Content>
										</DropdownMenu.Root>

										<!-- Group Link Button -->
										{#if selectedGroup}
											{@const groupLink = getGroupLink(selectedGroup)}
											{#if groupLink}
												<Button
													variant="outline"
													size="sm"
													class="text-xs text-gray-300 transition-all duration-200 border-gray-600 hover:bg-gray-700"
													on:click={() => window.open(groupLink.link, '_blank')}
												>
													{groupLink.label}
												</Button>
											{/if}
										{/if}
									</div>
								{/if}
							</div>
							{#if isLoggedIn}
								<form on:submit|preventDefault={handleSubmit} class="flex flex-col space-y-3">
									<div class="flex flex-col gap-2 sm:flex-row">
										<label for="comment-input" class="sr-only">Napisz komentarz</label>
										<textarea
											id="comment-input"
											bind:value={newComment}
											placeholder="Napisz komentarz..."
											class="min-h-[40px] grow resize-none overflow-hidden rounded-md! bg-gray-800 p-2 text-white"
											rows="1"
											maxlength="5000"
											on:input={autoResize}
											on:keydown={handleKeydown}
											disabled={isSubmittingComment}
										></textarea>
										<Button type="submit" disabled={isSubmittingComment || !newComment.trim()} class="cursor-pointer rounded-md! bg-[#ee8585] text-black hover:bg-[#8ec3f4] hover:text-black">
											{isSubmittingComment ? 'Wysyłanie...' : 'Wyślij komentarz'}
										</Button>
									</div>
									<div class="flex items-center gap-2">
										<label class="flex items-center gap-2 text-sm text-gray-300">
											<input type="checkbox" bind:checked={isSpoiler} class="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500" />
											Oznacz jako spoiler
										</label>
									</div>
								</form>
							{:else}
								<div class="p-4 text-center rounded-lg bg-gray-800/50">
									<p class="mb-2 text-gray-300">Zaloguj się, aby dodać komentarz</p>
									<Button on:click={() => (showUserModal = true)} class="cursor-pointer bg-[#ee8585] text-black hover:bg-[#8ec3f4]">Zaloguj się</Button>
								</div>
							{/if}
						</div>

						<div class="mt-4 md:ml-[-8px]">
							<InfiniteComments
								comments={initialized ? $commentsStore : []}
								pageSize={5}
								context="episode"
								animeId={anime.id}
								episodeNumber={episode.number}
								xlHeight="calc(100vh - 200px)"
								lgHeight="calc(100vh - 200px)"
								mdHeight="500px"
								smHeight="500px"
								enforceMaxDepth={false}
							/>
						</div>
					</div>
				</div>

				<div class="hidden w-full p-4 bg-gray-800 hide-scrollbar lg:block lg:min-h-screen lg:w-1/4 lg:overflow-y-auto">
					<h3 class="mb-4 text-lg font-semibold text-white">Odcinki</h3>
					<ul class="space-y-2">
						{#each Array(anime.totalEpisodes || 12) as _, i}
							{@const episodeNum = i + 1}
							{@const ep = allEpisodes.find((e) => e.number === episodeNum) || {
								number: episodeNum,
								title: `Odcinek ${episodeNum}`,
								thumbnail: anime.poster,
								airDate: (() => {
									const existingEpisode = anime.episodes.find((ep) => ep.number === episodeNum);
									if (existingEpisode) {
										return new Date(existingEpisode.airDate);
									}

									if (anime.nextAiringEpisode?.episode === episodeNum) {
										return new Date(anime.nextAiringEpisode.airingAt);
									}

									// Guard against empty episodes array
									if (!anime.episodes.length && !anime.nextAiringEpisode) {
										return new Date(); // Default to current date or another fallback
									}

									const lastKnownEpisode = anime.nextAiringEpisode || (anime.episodes.length > 0 ? anime.episodes[anime.episodes.length - 1] : null);

									// If there's no last known episode, return a default date
									if (!lastKnownEpisode) {
										return new Date();
									}

									const lastKnownDate = new Date(anime.nextAiringEpisode?.airingAt || (anime.episodes.length > 0 ? anime.episodes[anime.episodes.length - 1].airDate : Date.now()));

									const episodesDiff = episodeNum - lastKnownEpisode.episode;
									return new Date(lastKnownDate.getTime() + episodesDiff * 7 * 24 * 60 * 60 * 1000);
								})(),
								progress: 0,
								watched: false
							}}
							{@const now = new Date()}
							{@const episodeDate = new Date(ep.airDate)}
							{@const isReleased = episodeDate <= now}
							{@const wkrotce = isWkrotce(ep)}
							{@const styles = {
								opacity: wkrotce ? '0.4' : isReleased ? (ep.progress === 100 || ep.watched ? '0.5' : '0.9') : '0.4',
								cursor: isReleased && !wkrotce ? 'pointer' : 'not-allowed',
								pointerEvents: isReleased && !wkrotce ? 'auto' : 'none'
							}}
							<li>
								<a
									href={isReleased && !wkrotce ? `${getBaseUrl()}${ep.number}` : null}
									class="group flex cursor-pointer items-center space-x-2 rounded-md p-2 transition-all hover:bg-gray-700
									{ep.number === episode.number ? 'bg-gray-700' : ''} 
									{!isReleased || wkrotce ? 'cursor-not-allowed opacity-50' : ''}"
									style="opacity: {styles.opacity}; cursor: {styles.cursor}; pointer-events: {styles.pointerEvents};"
									aria-current={ep.number === episode.number ? 'true' : 'false'}
									aria-label="Odcinek {ep.number}: {ep.title}"
								>
									<div class="w-24 h-16 overflow-hidden bg-gray-800 rounded shrink-0">
										<img src={ep.thumbnail} alt="" class="object-cover w-full h-full opacity-" />
									</div>
									<div class="flex flex-col flex-1 min-w-0">
										<h4 class="truncate text-sm font-semibold text-white {getWatchedClass(ep.number)}">
											{ep.number}. {ep.title}
										</h4>
										<span class="text-xs text-gray-400">
											{#if getEpisodeTimeStatus(ep.airDate, false, ep.number) === 'delayed-airing-text'}
												<span class="sm:hidden">Poza harmonogramem</span>
												<span class="hidden sm:inline">Poza harmonogramem - jak zrobimy to będzie</span>
											{:else}
												{getEpisodeTimeStatus(ep.airDate, false, ep.number)}
											{/if}
										</span>
										<div class="w-full h-1 mt-1 overflow-hidden bg-gray-600 rounded-full">
											<div
												class="h-full rounded-full"
												style="width: {getPercentageCompletion(ep.number)}%;
												background-color: {dominantColor};"
												role="progressbar"
												aria-valuenow={ep.progress}
												aria-valuemin="0"
												aria-valuemax="100"
												aria-label="Postęp odcinka {ep.number}: {ep.progress}%"
											/>
										</div>
									</div>
								</a>
							</li>
						{/each}
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<UserProfileModal bind:open={showUserModal} user={$page.data.user} onClose={() => (showUserModal = false)} />

<style>
	.hide-scrollbar::-webkit-scrollbar {
		display: none;
	}

	.hide-scrollbar {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	textarea {
		font-family: inherit;
		line-height: 1.5;
		max-height: 200px;
	}

	@media (max-width: 640px) {
		textarea {
			min-height: 80px;
		}
	}

	form {
		gap: 0.5rem;
	}

	textarea,
	button {
		outline: none;
		-webkit-tap-highlight-color: transparent;
		transition:
			border-color 0.2s ease,
			box-shadow 0.2s ease;
	}

	textarea:focus {
		border: 1px solid rgba(99, 102, 241, 0.4);
		box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.1);
	}

	textarea::-moz-focus-inner,
	button::-moz-focus-inner {
		border: 0;
	}

	textarea {
		border: 1px solid rgba(75, 85, 99, 0.4);
	}

	@media (max-width: 640px) {
		textarea:focus {
			background-color: rgba(17, 24, 39, 1);
		}
	}

	/* Override default dropdown hover behavior for selected items */
	:global(.no-hover-bg:hover) {
		background-color: rgb(55 65 81) !important; /* Keep bg-gray-700 */
	}

	:global(.no-hover-bg:focus) {
		background-color: rgb(55 65 81) !important; /* Keep bg-gray-700 */
	}

	/* Player container scrolling styles */
	.player-container-scroll {
		/* Improve scrolling on touch devices */
		-webkit-overflow-scrolling: touch;
		/* Ensure scrolling works on all devices */
		overscroll-behavior: contain;
		/* Custom scrollbar styling */
		scrollbar-width: thin;
		scrollbar-color: #4b5563 #374151;
	}

	.player-container-scroll::-webkit-scrollbar {
		width: 8px;
	}

	.player-container-scroll::-webkit-scrollbar-track {
		background: #374151;
		border-radius: 4px;
	}

	.player-container-scroll::-webkit-scrollbar-thumb {
		background: #4b5563;
		border-radius: 4px;
	}

	.player-container-scroll::-webkit-scrollbar-thumb:hover {
		background: #6b7280;
	}
</style>
